import * as functions from 'firebase-functions'
import * as admin from 'firebase-admin'
import axios from 'axios'
import { Request, Response } from 'express'

// M-Pesa Configuration
const MPESA_CONFIG = {
  consumerKey: functions.config().mpesa.consumer_key,
  consumerSecret: functions.config().mpesa.consumer_secret,
  businessShortCode: functions.config().mpesa.business_short_code,
  passkey: functions.config().mpesa.passkey,
  environment: functions.config().mpesa.environment || 'sandbox', // sandbox or production
}

// M-Pesa URLs
const MPESA_URLS = {
  sandbox: {
    oauth: 'https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials',
    stkPush: 'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest',
    stkQuery: 'https://sandbox.safaricom.co.ke/mpesa/stkpushquery/v1/query',
  },
  production: {
    oauth: 'https://api.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials',
    stkPush: 'https://api.safaricom.co.ke/mpesa/stkpush/v1/processrequest',
    stkQuery: 'https://api.safaricom.co.ke/mpesa/stkpushquery/v1/query',
  }
}

interface MpesaSTKPushRequest {
  orderId: string
  phoneNumber: string
  amount: number
  accountReference: string
  transactionDesc: string
}

interface MpesaSTKPushResponse {
  MerchantRequestID: string
  CheckoutRequestID: string
  ResponseCode: string
  ResponseDescription: string
  CustomerMessage: string
}

interface MpesaCallbackData {
  Body: {
    stkCallback: {
      MerchantRequestID: string
      CheckoutRequestID: string
      ResultCode: number
      ResultDesc: string
      CallbackMetadata?: {
        Item: Array<{
          Name: string
          Value: string | number
        }>
      }
    }
  }
}

// Get M-Pesa OAuth Token
async function getMpesaToken(): Promise<string> {
  try {
    const auth = Buffer.from(`${MPESA_CONFIG.consumerKey}:${MPESA_CONFIG.consumerSecret}`).toString('base64')
    
    const response = await axios.get(MPESA_URLS[MPESA_CONFIG.environment].oauth, {
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json'
      }
    })

    return response.data.access_token
  } catch (error) {
    console.error('Error getting M-Pesa token:', error)
    throw new Error('Failed to get M-Pesa access token')
  }
}

// Generate M-Pesa Password
function generateMpesaPassword(): { password: string, timestamp: string } {
  const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3)
  const password = Buffer.from(
    `${MPESA_CONFIG.businessShortCode}${MPESA_CONFIG.passkey}${timestamp}`
  ).toString('base64')
  
  return { password, timestamp }
}

// Format phone number for M-Pesa
function formatPhoneNumber(phone: string): string {
  // Remove any non-digit characters
  let cleaned = phone.replace(/\D/g, '')
  
  // Handle different formats
  if (cleaned.startsWith('0')) {
    cleaned = '254' + cleaned.slice(1)
  } else if (cleaned.startsWith('7') || cleaned.startsWith('1')) {
    cleaned = '254' + cleaned
  } else if (!cleaned.startsWith('254')) {
    cleaned = '254' + cleaned
  }
  
  return cleaned
}

// Initiate STK Push
export const initiateMpesaPayment = functions.https.onCall(async (data: MpesaSTKPushRequest, context) => {
  try {
    // Verify authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated')
    }

    // Validate input
    if (!data.orderId || !data.phoneNumber || !data.amount) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields')
    }

    // Get order details
    const orderDoc = await admin.firestore().collection('orders').doc(data.orderId).get()
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Order not found')
    }

    const order = orderDoc.data()
    if (order?.userId !== context.auth.uid) {
      throw new functions.https.HttpsError('permission-denied', 'Access denied')
    }

    // Get M-Pesa token
    const token = await getMpesaToken()
    const { password, timestamp } = generateMpesaPassword()
    const formattedPhone = formatPhoneNumber(data.phoneNumber)

    // Prepare STK Push request
    const stkPushData = {
      BusinessShortCode: MPESA_CONFIG.businessShortCode,
      Password: password,
      Timestamp: timestamp,
      TransactionType: 'CustomerPayBillOnline',
      Amount: Math.round(data.amount),
      PartyA: formattedPhone,
      PartyB: MPESA_CONFIG.businessShortCode,
      PhoneNumber: formattedPhone,
      CallBackURL: `${functions.config().app.url}/api/payments/mpesa/callback`,
      AccountReference: data.accountReference,
      TransactionDesc: data.transactionDesc
    }

    // Make STK Push request
    const response = await axios.post(
      MPESA_URLS[MPESA_CONFIG.environment].stkPush,
      stkPushData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    )

    const mpesaResponse: MpesaSTKPushResponse = response.data

    // Save payment request to database
    await admin.firestore().collection('payments').doc(data.orderId).set({
      orderId: data.orderId,
      userId: context.auth.uid,
      merchantRequestId: mpesaResponse.MerchantRequestID,
      checkoutRequestId: mpesaResponse.CheckoutRequestID,
      phoneNumber: formattedPhone,
      amount: data.amount,
      status: 'pending',
      provider: 'mpesa',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      mpesaResponse: mpesaResponse
    })

    // Update order status
    await admin.firestore().collection('orders').doc(data.orderId).update({
      'payment.status': 'processing',
      'payment.method': 'mpesa',
      'payment.transactionId': mpesaResponse.CheckoutRequestID,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    })

    return {
      success: true,
      merchantRequestId: mpesaResponse.MerchantRequestID,
      checkoutRequestId: mpesaResponse.CheckoutRequestID,
      customerMessage: mpesaResponse.CustomerMessage
    }

  } catch (error) {
    console.error('M-Pesa STK Push error:', error)
    
    if (error instanceof functions.https.HttpsError) {
      throw error
    }
    
    throw new functions.https.HttpsError('internal', 'Payment initiation failed')
  }
})

// M-Pesa Callback Handler
export const mpesaCallback = functions.https.onRequest(async (req: Request, res: Response) => {
  try {
    const callbackData: MpesaCallbackData = req.body

    console.log('M-Pesa Callback received:', JSON.stringify(callbackData, null, 2))

    const stkCallback = callbackData.Body.stkCallback
    const { MerchantRequestID, CheckoutRequestID, ResultCode, ResultDesc } = stkCallback

    // Find the payment record
    const paymentQuery = await admin.firestore()
      .collection('payments')
      .where('checkoutRequestId', '==', CheckoutRequestID)
      .limit(1)
      .get()

    if (paymentQuery.empty) {
      console.error('Payment record not found for CheckoutRequestID:', CheckoutRequestID)
      res.status(200).json({ ResultCode: 0, ResultDesc: 'Success' })
      return
    }

    const paymentDoc = paymentQuery.docs[0]
    const payment = paymentDoc.data()

    if (ResultCode === 0) {
      // Payment successful
      const callbackMetadata = stkCallback.CallbackMetadata?.Item || []
      const mpesaReceiptNumber = callbackMetadata.find(item => item.Name === 'MpesaReceiptNumber')?.Value as string
      const transactionDate = callbackMetadata.find(item => item.Name === 'TransactionDate')?.Value as string
      const phoneNumber = callbackMetadata.find(item => item.Name === 'PhoneNumber')?.Value as string

      // Update payment record
      await paymentDoc.ref.update({
        status: 'completed',
        mpesaReceiptNumber: mpesaReceiptNumber,
        transactionDate: transactionDate,
        phoneNumber: phoneNumber,
        resultCode: ResultCode,
        resultDesc: ResultDesc,
        completedAt: admin.firestore.FieldValue.serverTimestamp(),
        callbackData: callbackData
      })

      // Update order
      await admin.firestore().collection('orders').doc(payment.orderId).update({
        status: 'paid',
        'payment.status': 'completed',
        'payment.mpesaReceiptNumber': mpesaReceiptNumber,
        'payment.paidAt': admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      })

      // Send confirmation email/SMS
      await sendPaymentConfirmation(payment.orderId, payment.userId)

    } else {
      // Payment failed
      await paymentDoc.ref.update({
        status: 'failed',
        resultCode: ResultCode,
        resultDesc: ResultDesc,
        failedAt: admin.firestore.FieldValue.serverTimestamp(),
        callbackData: callbackData
      })

      // Update order
      await admin.firestore().collection('orders').doc(payment.orderId).update({
        'payment.status': 'failed',
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      })
    }

    res.status(200).json({ ResultCode: 0, ResultDesc: 'Success' })

  } catch (error) {
    console.error('M-Pesa callback error:', error)
    res.status(500).json({ ResultCode: 1, ResultDesc: 'Internal Server Error' })
  }
})

// Query STK Push Status
export const queryMpesaPayment = functions.https.onCall(async (data: { checkoutRequestId: string }, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated')
    }

    const token = await getMpesaToken()
    const { password, timestamp } = generateMpesaPassword()

    const queryData = {
      BusinessShortCode: MPESA_CONFIG.businessShortCode,
      Password: password,
      Timestamp: timestamp,
      CheckoutRequestID: data.checkoutRequestId
    }

    const response = await axios.post(
      MPESA_URLS[MPESA_CONFIG.environment].stkQuery,
      queryData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    )

    return response.data

  } catch (error) {
    console.error('M-Pesa query error:', error)
    throw new functions.https.HttpsError('internal', 'Payment query failed')
  }
})

// Send payment confirmation
async function sendPaymentConfirmation(orderId: string, userId: string) {
  try {
    // Get order and user details
    const [orderDoc, userDoc] = await Promise.all([
      admin.firestore().collection('orders').doc(orderId).get(),
      admin.firestore().collection('users').doc(userId).get()
    ])

    if (!orderDoc.exists || !userDoc.exists) {
      console.error('Order or user not found for confirmation')
      return
    }

    const order = orderDoc.data()
    const user = userDoc.data()

    // Add to email queue (to be processed by email function)
    await admin.firestore().collection('emailQueue').add({
      to: user?.email,
      template: 'payment-confirmation',
      data: {
        customerName: `${user?.firstName} ${user?.lastName}`,
        orderNumber: order?.orderNumber,
        amount: order?.total,
        items: order?.items
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    })

    console.log('Payment confirmation queued for:', user?.email)

  } catch (error) {
    console.error('Error sending payment confirmation:', error)
  }
}
