export interface Product {
  id: string
  name: string
  price: number
  description: string
  images: string[]
  category: string
  sizes: string[]
  isCustomizable: boolean
  inStock: boolean
  featured?: boolean
}

export interface CartItem {
  id: string
  productId: string
  name: string
  price: number
  image: string
  size: string
  quantity: number
  customization?: {
    name?: string
    number?: string
    font?: string
    color?: string
    patches?: string[]
  }
}

export interface CustomizationOptions {
  fonts: { id: string; name: string; preview: string }[]
  colors: { id: string; name: string; hex: string }[]
  patches: { id: string; name: string; image: string; price: number }[]
}

export interface Order {
  id: string
  items: CartItem[]
  totalAmount: number
  shippingAddress: ShippingAddress
  paymentMethod: 'M-Pesa' | 'Card' | 'PayPal'
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded'
  orderStatus: 'processing' | 'shipped' | 'delivered'
  createdAt: Date
  updatedAt: Date
}

export interface ShippingAddress {
  fullName: string
  email: string
  phone: string
  street: string
  city: string
  state: string
  zipCode: string
  country: string
}
