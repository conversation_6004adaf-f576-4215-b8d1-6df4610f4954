# Nile Sport Jersey Store - Complete Backend Architecture

## 🏗️ System Architecture Overview

### Technology Stack
- **Backend**: Firebase (Firestore, Cloud Functions, Authentication, Storage)
- **Payment**: Safaricom M-Pesa API, Stripe (for international cards)
- **Email**: SendGrid or Firebase Extensions
- **Image Processing**: Firebase Storage + Cloud Functions
- **Analytics**: Firebase Analytics + Google Analytics
- **Monitoring**: Firebase Performance + Crashlytics

### Architecture Diagram
```
Frontend (Next.js) 
    ↓
Firebase Authentication
    ↓
Cloud Functions (API Layer)
    ↓
Firestore Database ← → Firebase Storage
    ↓
External APIs (M-Pesa, SendGrid, etc.)
```

## 📊 Database Schema (Firestore)

### 1. Users Collection (`/users/{userId}`)
```typescript
interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone: string
  dateOfBirth?: string
  addresses: Address[]
  preferences: {
    favoriteTeams: string[]
    preferredSize: string
    emailNotifications: boolean
    smsNotifications: boolean
  }
  loyaltyPoints: number
  createdAt: Timestamp
  updatedAt: Timestamp
  lastLoginAt: Timestamp
}

interface Address {
  id: string
  type: 'shipping' | 'billing'
  firstName: string
  lastName: string
  street: string
  city: string
  state: string
  zipCode: string
  country: string
  phone: string
  isDefault: boolean
}
```

### 2. Products Collection (`/products/{productId}`)
```typescript
interface Product {
  id: string
  name: string
  description: string
  category: 'Club' | 'National' | 'Retro' | 'Training'
  subcategory: string // e.g., 'Premier League', 'La Liga'
  team: string
  season: string
  price: number
  compareAtPrice?: number
  images: string[] // Firebase Storage URLs
  sizes: ProductSize[]
  colors: ProductColor[]
  isCustomizable: boolean
  customizationOptions: CustomizationOptions
  tags: string[]
  featured: boolean
  status: 'active' | 'draft' | 'archived'
  seo: {
    title: string
    description: string
    keywords: string[]
  }
  inventory: {
    trackQuantity: boolean
    totalStock: number
    lowStockThreshold: number
    sizeStock: Record<string, number>
  }
  createdAt: Timestamp
  updatedAt: Timestamp
}

interface ProductSize {
  size: string
  measurements: {
    chest: number
    length: number
    sleeve: number
  }
  stock: number
}

interface ProductColor {
  name: string
  hex: string
  images: string[]
}

interface CustomizationOptions {
  maxNameLength: number
  maxNumberLength: number
  availableFonts: string[]
  availableColors: string[]
  availablePatches: Patch[]
  namingRules: string[]
  additionalCost: number
}

interface Patch {
  id: string
  name: string
  image: string
  price: number
  category: 'league' | 'champion' | 'custom'
}
```

### 3. Orders Collection (`/orders/{orderId}`)
```typescript
interface Order {
  id: string
  userId: string
  orderNumber: string // Human-readable order number
  status: OrderStatus
  items: OrderItem[]
  subtotal: number
  shipping: number
  tax: number
  discount: number
  total: number
  currency: 'KES'
  
  // Customer Info
  customer: {
    email: string
    firstName: string
    lastName: string
    phone: string
  }
  
  // Addresses
  shippingAddress: Address
  billingAddress: Address
  
  // Payment
  payment: {
    method: 'mpesa' | 'card' | 'bank_transfer'
    status: PaymentStatus
    transactionId: string
    mpesaReceiptNumber?: string
    paidAt?: Timestamp
  }
  
  // Shipping
  shipping: {
    method: string
    carrier: string
    trackingNumber?: string
    estimatedDelivery: Timestamp
    shippedAt?: Timestamp
    deliveredAt?: Timestamp
  }
  
  // Metadata
  notes?: string
  internalNotes?: string
  source: 'web' | 'mobile' | 'admin'
  createdAt: Timestamp
  updatedAt: Timestamp
}

type OrderStatus = 
  | 'pending_payment'
  | 'paid'
  | 'processing'
  | 'customizing'
  | 'shipped'
  | 'delivered'
  | 'cancelled'
  | 'refunded'

type PaymentStatus = 
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'refunded'

interface OrderItem {
  id: string
  productId: string
  productName: string
  productImage: string
  size: string
  color?: string
  quantity: number
  unitPrice: number
  totalPrice: number
  customization?: {
    name?: string
    number?: string
    font: string
    color: string
    patches: string[]
    additionalCost: number
    previewImage?: string
  }
}
```

### 4. Categories Collection (`/categories/{categoryId}`)
```typescript
interface Category {
  id: string
  name: string
  slug: string
  description: string
  image: string
  parentId?: string
  level: number
  sortOrder: number
  isActive: boolean
  seo: {
    title: string
    description: string
    keywords: string[]
  }
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

### 5. Cart Collection (`/carts/{userId}`)
```typescript
interface Cart {
  userId: string
  items: CartItem[]
  subtotal: number
  estimatedShipping: number
  estimatedTax: number
  estimatedTotal: number
  currency: 'KES'
  expiresAt: Timestamp
  updatedAt: Timestamp
}

interface CartItem {
  id: string
  productId: string
  size: string
  color?: string
  quantity: number
  unitPrice: number
  customization?: OrderItem['customization']
  addedAt: Timestamp
}
```

### 6. Customizations Collection (`/customizations/{customizationId}`)
```typescript
interface Customization {
  id: string
  userId: string
  productId: string
  orderId?: string
  status: 'draft' | 'pending_approval' | 'approved' | 'in_production' | 'completed'
  
  details: {
    name?: string
    number?: string
    font: string
    color: string
    patches: string[]
    specialInstructions?: string
  }
  
  images: {
    original: string
    preview: string
    final?: string
  }
  
  pricing: {
    baseCost: number
    customizationCost: number
    patchesCost: number
    totalCost: number
  }
  
  production: {
    estimatedDays: number
    startedAt?: Timestamp
    completedAt?: Timestamp
    notes?: string
  }
  
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

### 7. Inventory Collection (`/inventory/{productId}`)
```typescript
interface InventoryItem {
  productId: string
  totalStock: number
  reservedStock: number
  availableStock: number
  lowStockThreshold: number
  sizeBreakdown: Record<string, {
    stock: number
    reserved: number
    available: number
  }>
  lastRestocked: Timestamp
  supplier: string
  cost: number
  updatedAt: Timestamp
}
```

### 8. Reviews Collection (`/reviews/{reviewId}`)
```typescript
interface Review {
  id: string
  productId: string
  userId: string
  orderId: string
  rating: number // 1-5
  title: string
  content: string
  images?: string[]
  verified: boolean
  helpful: number
  reported: number
  status: 'pending' | 'approved' | 'rejected'
  createdAt: Timestamp
  updatedAt: Timestamp
}
```

### 9. Analytics Collection (`/analytics/{date}`)
```typescript
interface DailyAnalytics {
  date: string // YYYY-MM-DD
  sales: {
    totalRevenue: number
    totalOrders: number
    averageOrderValue: number
    topProducts: Array<{productId: string, revenue: number, quantity: number}>
  }
  traffic: {
    pageViews: number
    uniqueVisitors: number
    conversionRate: number
  }
  inventory: {
    lowStockAlerts: string[]
    outOfStockItems: string[]
  }
  customizations: {
    totalRequests: number
    completedRequests: number
    averageCompletionTime: number
  }
}
```

### 10. Settings Collection (`/settings/config`)
```typescript
interface AppSettings {
  store: {
    name: string
    description: string
    logo: string
    favicon: string
    currency: string
    timezone: string
    language: string
  }
  shipping: {
    freeShippingThreshold: number
    standardRate: number
    expressRate: number
    internationalRate: number
    processingDays: number
  }
  payments: {
    mpesa: {
      enabled: boolean
      businessShortCode: string
      passkey: string
    }
    stripe: {
      enabled: boolean
      publishableKey: string
    }
  }
  customization: {
    maxProcessingDays: number
    rushOrderSurcharge: number
    qualityCheckDays: number
  }
  notifications: {
    orderConfirmation: boolean
    shippingUpdates: boolean
    customizationUpdates: boolean
    promotions: boolean
  }
}
```

## 🔐 Security Rules

### Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Products are publicly readable, admin writable
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Orders are user-specific
    match /orders/{orderId} {
      allow read, write: if request.auth != null && 
        (resource.data.userId == request.auth.uid || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    // Carts are user-specific
    match /carts/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Categories are publicly readable
    match /categories/{categoryId} {
      allow read: if true;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
```

## 🚀 API Endpoints (Cloud Functions)

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/forgot-password` - Password reset
- `POST /api/auth/verify-email` - Email verification

### Product Endpoints
- `GET /api/products` - List products with filtering/pagination
- `GET /api/products/{id}` - Get single product
- `POST /api/products` - Create product (admin)
- `PUT /api/products/{id}` - Update product (admin)
- `DELETE /api/products/{id}` - Delete product (admin)
- `GET /api/products/search` - Search products

### Cart Endpoints
- `GET /api/cart` - Get user's cart
- `POST /api/cart/add` - Add item to cart
- `PUT /api/cart/update` - Update cart item
- `DELETE /api/cart/remove` - Remove cart item
- `DELETE /api/cart/clear` - Clear entire cart

### Order Endpoints
- `POST /api/orders` - Create new order
- `GET /api/orders` - Get user's orders
- `GET /api/orders/{id}` - Get specific order
- `PUT /api/orders/{id}/cancel` - Cancel order
- `POST /api/orders/{id}/refund` - Request refund

### Payment Endpoints
- `POST /api/payments/mpesa/initiate` - Initiate M-Pesa payment
- `POST /api/payments/mpesa/callback` - M-Pesa callback handler
- `POST /api/payments/stripe/intent` - Create Stripe payment intent
- `POST /api/payments/verify` - Verify payment status

### Customization Endpoints
- `POST /api/customizations` - Create customization request
- `GET /api/customizations/{id}` - Get customization details
- `PUT /api/customizations/{id}` - Update customization
- `POST /api/customizations/{id}/preview` - Generate preview image

This architecture provides a solid foundation for a scalable, secure e-commerce platform. The next step would be implementing each component systematically.
