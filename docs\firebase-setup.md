# Firebase Project Setup Guide

## 🚀 Initial Firebase Setup

### 1. Create Firebase Project
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase project
firebase init
```

### 2. Project Configuration
Select the following services during initialization:
- ✅ Firestore (Database)
- ✅ Functions (Cloud Functions)
- ✅ Hosting (Web hosting)
- ✅ Storage (File storage)
- ✅ Emulators (Local development)

### 3. Install Dependencies
```bash
# Frontend dependencies
npm install firebase firebase-admin
npm install @firebase/app @firebase/auth @firebase/firestore @firebase/storage

# Cloud Functions dependencies (in functions folder)
cd functions
npm install firebase-admin firebase-functions
npm install cors express helmet
npm install @types/cors @types/express
```

## 📁 Project Structure
```
nile-sport-jersey-store/
├── src/                          # Next.js frontend
├── functions/                    # Cloud Functions
│   ├── src/
│   │   ├── index.ts             # Main functions entry
│   │   ├── auth/                # Authentication functions
│   │   ├── products/            # Product management
│   │   ├── orders/              # Order processing
│   │   ├── payments/            # Payment handling
│   │   └── utils/               # Shared utilities
│   ├── package.json
│   └── tsconfig.json
├── firestore.rules              # Security rules
├── storage.rules                # Storage security rules
├── firebase.json                # Firebase configuration
└── .firebaserc                  # Project aliases
```

## 🔧 Firebase Configuration Files

### firebase.json
```json
{
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "functions": {
    "source": "functions",
    "runtime": "nodejs18",
    "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]
  },
  "hosting": {
    "public": "out",
    "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  },
  "storage": {
    "rules": "storage.rules"
  },
  "emulators": {
    "auth": {
      "port": 9099
    },
    "functions": {
      "port": 5001
    },
    "firestore": {
      "port": 8080
    },
    "storage": {
      "port": 9199
    },
    "ui": {
      "enabled": true,
      "port": 4000
    }
  }
}
```

### .firebaserc
```json
{
  "projects": {
    "default": "nile-sport-jersey-store",
    "staging": "nile-sport-staging",
    "production": "nile-sport-production"
  }
}
```

## 🔐 Environment Configuration

### Frontend Environment (.env.local)
```bash
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# M-Pesa Configuration
NEXT_PUBLIC_MPESA_ENVIRONMENT=sandbox # or production
NEXT_PUBLIC_MPESA_BUSINESS_SHORT_CODE=174379

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:5001/your_project_id/us-central1
```

### Cloud Functions Environment
```bash
# In functions folder
firebase functions:config:set mpesa.consumer_key="your_consumer_key"
firebase functions:config:set mpesa.consumer_secret="your_consumer_secret"
firebase functions:config:set mpesa.passkey="your_passkey"
firebase functions:config:set mpesa.business_short_code="174379"

firebase functions:config:set sendgrid.api_key="your_sendgrid_key"
firebase functions:config:set app.url="https://your-domain.com"
```

## 🔥 Firebase SDK Configuration

### lib/firebase.ts
```typescript
import { initializeApp, getApps } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'
import { getStorage } from 'firebase/storage'
import { getFunctions } from 'firebase/functions'

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
}

// Initialize Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0]

// Initialize Firebase services
export const auth = getAuth(app)
export const db = getFirestore(app)
export const storage = getStorage(app)
export const functions = getFunctions(app)

export default app
```

## 🛡️ Security Rules

### firestore.rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Users collection
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow read: if isAdmin();
    }
    
    // Products collection
    match /products/{productId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Orders collection
    match /orders/{orderId} {
      allow read, write: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    // Carts collection
    match /carts/{userId} {
      allow read, write: if isOwner(userId);
    }
    
    // Categories collection
    match /categories/{categoryId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Customizations collection
    match /customizations/{customizationId} {
      allow read, write: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    // Reviews collection
    match /reviews/{reviewId} {
      allow read: if true;
      allow create: if isAuthenticated() && request.auth.uid == request.resource.data.userId;
      allow update, delete: if isAuthenticated() && 
        (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    // Analytics (admin only)
    match /analytics/{document=**} {
      allow read, write: if isAdmin();
    }
    
    // Settings (admin only)
    match /settings/{document=**} {
      allow read, write: if isAdmin();
    }
  }
}
```

### storage.rules
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Product images (public read, admin write)
    match /products/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // User uploads (authenticated users)
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Customization previews
    match /customizations/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Order attachments
    match /orders/{orderId}/{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 🚀 Deployment Commands

### Development
```bash
# Start emulators
firebase emulators:start

# Deploy functions only
firebase deploy --only functions

# Deploy hosting only
firebase deploy --only hosting

# Deploy firestore rules
firebase deploy --only firestore:rules
```

### Production
```bash
# Deploy everything
firebase deploy

# Deploy to specific project
firebase use production
firebase deploy

# Deploy with specific target
firebase target:apply hosting production your-production-site
firebase deploy --only hosting:production
```

## 📊 Firestore Indexes

### firestore.indexes.json
```json
{
  "indexes": [
    {
      "collectionGroup": "products",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "category", "order": "ASCENDING"},
        {"fieldPath": "featured", "order": "DESCENDING"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "products",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "status", "order": "ASCENDING"},
        {"fieldPath": "price", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "orders",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "userId", "order": "ASCENDING"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "orders",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "status", "order": "ASCENDING"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    }
  ],
  "fieldOverrides": []
}
```

This setup provides a complete Firebase foundation for the jersey store with proper security, scalability, and development workflow.
