import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  User,
  AuthError
} from 'firebase/auth'
import { doc, setDoc, getDoc, updateDoc, Timestamp } from 'firebase/firestore'
import { auth, db } from './firebase'
import { User as AppUser } from '@/types'

export interface AuthResult {
  success: boolean
  user?: User
  error?: string
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  phone: string
}

export interface LoginData {
  email: string
  password: string
}

// Register new user
export async function registerUser(data: RegisterData): Promise<AuthResult> {
  try {
    // Create Firebase Auth user
    const userCredential = await createUserWithEmailAndPassword(auth, data.email, data.password)
    const user = userCredential.user

    // Update display name
    await updateProfile(user, {
      displayName: `${data.firstName} ${data.lastName}`
    })

    // Create user document in Firestore
    const userData: AppUser = {
      id: user.uid,
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      phone: data.phone,
      addresses: [],
      preferences: {
        favoriteTeams: [],
        preferredSize: '',
        emailNotifications: true,
        smsNotifications: true
      },
      loyaltyPoints: 0,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      lastLoginAt: Timestamp.now()
    }

    await setDoc(doc(db, 'users', user.uid), userData)

    return { success: true, user }
  } catch (error) {
    console.error('Registration error:', error)
    return { 
      success: false, 
      error: getAuthErrorMessage(error as AuthError) 
    }
  }
}

// Login user
export async function loginUser(data: LoginData): Promise<AuthResult> {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, data.email, data.password)
    const user = userCredential.user

    // Update last login time
    await updateDoc(doc(db, 'users', user.uid), {
      lastLoginAt: Timestamp.now()
    })

    return { success: true, user }
  } catch (error) {
    console.error('Login error:', error)
    return { 
      success: false, 
      error: getAuthErrorMessage(error as AuthError) 
    }
  }
}

// Logout user
export async function logoutUser(): Promise<AuthResult> {
  try {
    await signOut(auth)
    return { success: true }
  } catch (error) {
    console.error('Logout error:', error)
    return { 
      success: false, 
      error: getAuthErrorMessage(error as AuthError) 
    }
  }
}

// Send password reset email
export async function resetPassword(email: string): Promise<AuthResult> {
  try {
    await sendPasswordResetEmail(auth, email)
    return { success: true }
  } catch (error) {
    console.error('Password reset error:', error)
    return { 
      success: false, 
      error: getAuthErrorMessage(error as AuthError) 
    }
  }
}

// Get user profile data
export async function getUserProfile(userId: string): Promise<AppUser | null> {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId))
    if (userDoc.exists()) {
      return userDoc.data() as AppUser
    }
    return null
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return null
  }
}

// Update user profile
export async function updateUserProfile(userId: string, updates: Partial<AppUser>): Promise<boolean> {
  try {
    await updateDoc(doc(db, 'users', userId), {
      ...updates,
      updatedAt: Timestamp.now()
    })
    return true
  } catch (error) {
    console.error('Error updating user profile:', error)
    return false
  }
}

// Convert Firebase Auth errors to user-friendly messages
function getAuthErrorMessage(error: AuthError): string {
  switch (error.code) {
    case 'auth/user-not-found':
      return 'No account found with this email address.'
    case 'auth/wrong-password':
      return 'Incorrect password. Please try again.'
    case 'auth/email-already-in-use':
      return 'An account with this email already exists.'
    case 'auth/weak-password':
      return 'Password should be at least 6 characters long.'
    case 'auth/invalid-email':
      return 'Please enter a valid email address.'
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.'
    case 'auth/network-request-failed':
      return 'Network error. Please check your connection.'
    default:
      return 'An error occurred. Please try again.'
  }
}

// Validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Validate phone number (Kenyan format)
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^(\+254|254|0)?[17]\d{8}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

// Validate password strength
export function validatePassword(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}
