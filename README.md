# Nile Sport - Premium Jersey Store

A modern e-commerce platform for authentic and custom jerseys built with Next.js, TypeScript, and Tailwind CSS.

## Features

- **Product Catalog**: Browse jerseys by category, team, and customization options
- **Jersey Customization**: Personalize jerseys with names, numbers, fonts, colors, and patches
- **Shopping Cart**: Add, remove, and manage items with persistent storage
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Modern UI**: Clean, professional design with smooth animations

## Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, shadcn/ui components
- **State Management**: Zustand
- **Images**: Next.js Image optimization
- **Icons**: Lucide React

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd nile-sport-jersey-store
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── cart/              # Shopping cart page
│   ├── checkout/          # Checkout process
│   ├── shop/              # Product listing and details
│   │   ├── [id]/          # Product detail page
│   │   └── customize/[id]/ # Jersey customization
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable UI components
│   ├── layout/           # Navigation, footer
│   ├── product/          # Product-related components
│   └── ui/               # Base UI components
├── data/                 # Sample data and utilities
├── lib/                  # Utility functions
├── store/                # Zustand state management
└── types/                # TypeScript type definitions
```

## Key Features

### 1. Product Catalog
- Grid and list view options
- Advanced filtering and sorting
- Category-based navigation
- Search functionality

### 2. Jersey Customization
- Real-time preview (AI-powered simulation)
- Custom name and number input
- Font style selection
- Color customization
- Patch and badge options

### 3. Shopping Cart
- Persistent cart storage
- Quantity management
- Customization details display
- Price calculations with shipping

### 4. Checkout Process
- Multi-step checkout form
- M-Pesa and card payment options
- Shipping address collection
- Order summary and confirmation

## Customization

### Adding New Products
Edit `src/data/products.ts` to add new jersey products with images, pricing, and customization options.

### Styling
The project uses Tailwind CSS with a custom design system. Modify `src/app/globals.css` and `tailwind.config.ts` for theme customization.

### State Management
Cart and application state is managed with Zustand. See `src/store/cart.ts` for the cart implementation.

## Deployment

The project is ready for deployment on Vercel, Netlify, or any platform supporting Next.js.

```bash
npm run build
npm start
```

## Future Enhancements

- Firebase backend integration
- M-Pesa payment processing
- User authentication
- Order management
- Admin dashboard
- Email notifications
- Inventory management

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## License

This project is licensed under the MIT License.
