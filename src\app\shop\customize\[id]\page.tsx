'use client'

import { useState } from 'react'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useCartStore } from '@/store/cart'
import { getProductById } from '@/data/products'
import { formatPrice } from '@/lib/utils'
import { ArrowLeft, Palette, Loader2 } from 'lucide-react'

const fontOptions = [
  { id: 'classic', name: 'Classic', preview: 'font-serif' },
  { id: 'bold', name: 'Bold', preview: 'font-bold' },
  { id: 'sporty', name: 'Sporty', preview: 'font-mono' },
  { id: 'modern', name: 'Modern', preview: 'font-sans' }
]

const colorOptions = [
  { id: 'white', name: 'White', hex: '#FFFFFF' },
  { id: 'black', name: 'Black', hex: '#000000' },
  { id: 'gold', name: 'Gold', hex: '#FFD700' },
  { id: 'silver', name: 'Silver', hex: '#C0C0C0' },
  { id: 'red', name: 'Red', hex: '#FF0000' },
  { id: 'blue', name: 'Blue', hex: '#0000FF' }
]

const patchOptions = [
  { id: 'none', name: 'No Patch', image: '', price: 0 },
  { id: 'league', name: 'League Patch', image: '/patches/league.png', price: 500 },
  { id: 'champion', name: 'Champion Patch', image: '/patches/champion.png', price: 800 },
  { id: 'custom', name: 'Custom Team Patch', image: '/patches/custom.png', price: 1000 }
]

export default function CustomizePage() {
  const params = useParams()
  const productId = params.id as string
  const product = getProductById(productId)
  
  const [customization, setCustomization] = useState({
    name: '',
    number: '',
    font: 'classic',
    color: 'white',
    patches: [] as string[]
  })
  
  const [selectedSize, setSelectedSize] = useState('')
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  
  const addItem = useCartStore((state) => state.addItem)

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <Link href="/shop">
            <Button>Back to Shop</Button>
          </Link>
        </div>
      </div>
    )
  }

  if (!product.isCustomizable) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">This product is not customizable</h1>
          <Link href={`/shop/${product.id}`}>
            <Button>View Product Details</Button>
          </Link>
        </div>
      </div>
    )
  }

  const selectedPatches = patchOptions.filter(patch => customization.patches.includes(patch.id))
  const patchPrice = selectedPatches.reduce((total, patch) => total + patch.price, 0)
  const totalPrice = product.price + patchPrice

  const handleCustomizationChange = (field: string, value: string) => {
    setCustomization(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handlePatchToggle = (patchId: string) => {
    if (patchId === 'none') {
      setCustomization(prev => ({ ...prev, patches: [] }))
      return
    }
    
    setCustomization(prev => ({
      ...prev,
      patches: prev.patches.includes(patchId)
        ? prev.patches.filter(id => id !== patchId)
        : [...prev.patches, patchId]
    }))
  }

  const generatePreview = async () => {
    setIsGeneratingPreview(true)
    
    // Simulate AI preview generation
    setTimeout(() => {
      setPreviewImage(product.images[0]) // For now, use the original image
      setIsGeneratingPreview(false)
    }, 2000)
  }

  const handleAddToCart = () => {
    if (!selectedSize) {
      alert('Please select a size')
      return
    }

    if (!customization.name || !customization.number) {
      alert('Please enter both name and number for customization')
      return
    }

    addItem({
      id: '',
      productId: product.id,
      name: `${product.name} (Custom)`,
      price: totalPrice,
      image: previewImage || product.images[0],
      size: selectedSize,
      quantity: 1,
      customization: {
        name: customization.name,
        number: customization.number,
        font: customization.font,
        color: customization.color,
        patches: customization.patches
      }
    })

    alert('Custom jersey added to cart!')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center">
            <Link href={`/shop/${product.id}`} className="mr-4">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Customize Jersey</h1>
              <p className="text-gray-600">{product.name}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Preview Section */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Palette className="h-5 w-5 mr-2" />
                  Jersey Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden relative">
                  {isGeneratingPreview ? (
                    <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90">
                      <div className="text-center">
                        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                        <p className="text-sm text-gray-600">Generating preview...</p>
                      </div>
                    </div>
                  ) : (
                    <Image
                      src={previewImage || product.images[0]}
                      alt="Jersey Preview"
                      width={500}
                      height={500}
                      className="w-full h-full object-cover"
                    />
                  )}
                  
                  {/* Overlay customization preview */}
                  {(customization.name || customization.number) && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        {customization.name && (
                          <div 
                            className={`text-2xl font-bold mb-2 ${fontOptions.find(f => f.id === customization.font)?.preview}`}
                            style={{ color: colorOptions.find(c => c.id === customization.color)?.hex }}
                          >
                            {customization.name}
                          </div>
                        )}
                        {customization.number && (
                          <div 
                            className={`text-4xl font-bold ${fontOptions.find(f => f.id === customization.font)?.preview}`}
                            style={{ color: colorOptions.find(c => c.id === customization.color)?.hex }}
                          >
                            {customization.number}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                
                <Button 
                  onClick={generatePreview}
                  disabled={isGeneratingPreview || !customization.name || !customization.number}
                  className="w-full mt-4"
                >
                  {isGeneratingPreview ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    'Generate AI Preview'
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Customization Options */}
          <div className="space-y-6">
            {/* Basic Customization */}
            <Card>
              <CardHeader>
                <CardTitle>Personalization</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Player Name
                  </label>
                  <Input
                    value={customization.name}
                    onChange={(e) => handleCustomizationChange('name', e.target.value)}
                    placeholder="Enter player name"
                    maxLength={15}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Jersey Number
                  </label>
                  <Input
                    type="number"
                    value={customization.number}
                    onChange={(e) => handleCustomizationChange('number', e.target.value)}
                    placeholder="Enter number (1-99)"
                    min="1"
                    max="99"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Font Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Font Style</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  {fontOptions.map((font) => (
                    <button
                      key={font.id}
                      onClick={() => handleCustomizationChange('font', font.id)}
                      className={`p-3 border rounded-lg text-center transition-colors ${
                        customization.font === font.id
                          ? 'border-primary bg-primary/10'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className={`text-lg ${font.preview}`}>{font.name}</div>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Color Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Text Color</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-3">
                  {colorOptions.map((color) => (
                    <button
                      key={color.id}
                      onClick={() => handleCustomizationChange('color', color.id)}
                      className={`p-3 border rounded-lg text-center transition-colors ${
                        customization.color === color.id
                          ? 'border-primary bg-primary/10'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div 
                        className="w-6 h-6 rounded-full mx-auto mb-1 border"
                        style={{ backgroundColor: color.hex }}
                      />
                      <div className="text-sm">{color.name}</div>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Patch Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Patches & Badges</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {patchOptions.map((patch) => (
                    <label
                      key={patch.id}
                      className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors ${
                        patch.id === 'none' 
                          ? (customization.patches.length === 0 ? 'border-primary bg-primary/10' : 'border-gray-200')
                          : (customization.patches.includes(patch.id) ? 'border-primary bg-primary/10' : 'border-gray-200')
                      }`}
                    >
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={patch.id === 'none' ? customization.patches.length === 0 : customization.patches.includes(patch.id)}
                          onChange={() => handlePatchToggle(patch.id)}
                          className="sr-only"
                        />
                        <span className="font-medium">{patch.name}</span>
                      </div>
                      <span className="text-sm text-gray-600">
                        {patch.price > 0 ? `+${formatPrice(patch.price)}` : 'Free'}
                      </span>
                    </label>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Size & Add to Cart */}
            <Card>
              <CardHeader>
                <CardTitle>Complete Your Order</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Size
                  </label>
                  <Select
                    value={selectedSize}
                    onChange={(e) => setSelectedSize(e.target.value)}
                  >
                    <option value="">Select a size</option>
                    {product.sizes.map(size => (
                      <option key={size} value={size}>{size}</option>
                    ))}
                  </Select>
                </div>

                <div className="border-t pt-4">
                  <div className="flex justify-between items-center mb-4">
                    <span className="text-lg font-semibold">Total Price:</span>
                    <span className="text-2xl font-bold text-primary">
                      {formatPrice(totalPrice)}
                    </span>
                  </div>
                  
                  <Button
                    onClick={handleAddToCart}
                    disabled={!selectedSize || !customization.name || !customization.number}
                    className="w-full"
                    size="lg"
                  >
                    Add Custom Jersey to Cart
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
